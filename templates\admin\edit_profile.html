{% extends "base.html" %}

{% block title %}تعديل الملف الشخصي - نظام الشؤون القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل الملف الشخصي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ {% url 'admin_profile' %} }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للملف الشخصي
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <!-- صورة الملف الشخصي -->
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img id="profileImage" src="https://ui-avatars.com/api/?name={{ current_user.full_name }}&background=007bff&color=fff&size=150" 
                         class="rounded-circle mb-3" alt="صورة الملف الشخصي" width="150" height="150">
                </div>
                <h5>{{ current_user.full_name }}</h5>
                <p class="text-muted">{{ current_user.username }}@</p>
                
                <div class="d-grid gap-2">
                    <input type="file" id="profileImageInput" accept="image/*" style="display: none;">
                    <button type="button" class="btn btn-primary btn-sm" onclick="document.getElementById('profileImageInput').click()">
                        <i class="fas fa-camera me-1"></i>
                        تغيير الصورة
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeProfileImage()">
                        <i class="fas fa-trash me-1"></i>
                        إزالة الصورة
                    </button>
                </div>
            </div>
        </div>
        
        <!-- معلومات الحساب -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">معلومات الحساب</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">تاريخ التسجيل:</small>
                <p class="mb-2">{{ current_user.created_at|strftime("%Y-%m-%d") if current_user.created_at else 'غير محدد' }}</p>
                
                <small class="text-muted">آخر دخول:</small>
                <p class="mb-2">{{ current_user.last_login|strftime("%Y-%m-%d %H:%M") if current_user.last_login else 'الآن' }}</p>
                
                <small class="text-muted">الدور:</small>
                <p class="mb-0">
                    {% if current_user.role == 'admin' %}
                        <span class="badge bg-danger">مدير النظام</span>
                    {% elif current_user.role == 'lawyer' %}
                        <span class="badge bg-primary">محامي</span>
                    {% elif current_user.role == 'secretary' %}
                        <span class="badge bg-info">سكرتير</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ current_user.role }}</span>
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <!-- نموذج تعديل البيانات -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل البيانات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="{{ current_user.full_name }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ current_user.username }}" required readonly>
                            <div class="form-text">لا يمكن تغيير اسم المستخدم</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ current_user.email or '' }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ current_user.phone or '' }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="2">{{ current_user.address or '' }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="bio" class="form-label">نبذة شخصية</label>
                        <textarea class="form-control" id="bio" name="bio" rows="3" 
                                  placeholder="اكتب نبذة مختصرة عنك...">{{ current_user.bio or '' }}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                   value="{{ current_user.birth_date|strftime("%Y-%m-%d") if current_user.birth_date else '' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">اختر الجنس</option>
                                <option value="male" {% if current_user.gender == 'male' %}selected{% endif %}>ذكر</option>
                                <option value="female" {% if current_user.gender == 'female' %}selected{% endif %}>أنثى</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" 
                                   {% if current_user.email_notifications %}checked{% endif %}>
                            <label class="form-check-label" for="email_notifications">
                                تلقي الإشعارات عبر البريد الإلكتروني
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications" 
                                   {% if current_user.sms_notifications %}checked{% endif %}>
                            <label class="form-check-label" for="sms_notifications">
                                تلقي الإشعارات عبر الرسائل النصية
                            </label>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-secondary me-md-2" onclick="history.back()">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- تغيير كلمة المرور -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>
                    تغيير كلمة المرور
                </h5>
            </div>
            <div class="card-body">
                <form id="passwordForm">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" id="current_password" name="current_password">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-warning" onclick="changePassword()">
                            <i class="fas fa-key me-1"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// معالجة رفع صورة الملف الشخصي
document.getElementById('profileImageInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // التحقق من نوع الملف
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, JPEG, أو GIF');
            return;
        }
        
        // التحقق من حجم الملف (2MB كحد أقصى)
        if (file.size > 2 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت');
            return;
        }
        
        // معاينة الصورة
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('profileImage').src = e.target.result;
        };
        reader.readAsDataURL(file);
        
        // رفع الصورة (محاكاة)
        setTimeout(() => {
            alert('تم رفع الصورة بنجاح (وظيفة تجريبية)');
        }, 1000);
    }
});

function removeProfileImage() {
    if (confirm('هل أنت متأكد من رغبتك في إزالة صورة الملف الشخصي؟')) {
        document.getElementById('profileImage').src = 'https://ui-avatars.com/api/?name={{ current_user.full_name }}&background=007bff&color=fff&size=150';
        alert('تم إزالة الصورة بنجاح');
    }
}

// معالجة نموذج تعديل الملف الشخصي
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // التحقق من صحة البيانات
    const fullName = document.getElementById('full_name').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (!fullName) {
        alert('يرجى إدخال الاسم الكامل');
        return;
    }
    
    if (!email) {
        alert('يرجى إدخال البريد الإلكتروني');
        return;
    }
    
    // محاكاة حفظ البيانات
    alert('تم حفظ التغييرات بنجاح (وظيفة تجريبية)');
    
    // إعادة توجيه للملف الشخصي
    setTimeout(() => {
        window.location.href = '/admin/profile';
    }, 1000);
});

function changePassword() {
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (!currentPassword || !newPassword || !confirmPassword) {
        alert('يرجى ملء جميع حقول كلمة المرور');
        return;
    }
    
    if (newPassword !== == confirmPassword) {
        alert('كلمات المرور الجديدة غير متطابقة');
        return;
    }
    
    if (newPassword.length < 8) {
        alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
        return;
    }
    
    // محاكاة تغيير كلمة المرور
    alert('تم تغيير كلمة المرور بنجاح (وظيفة تجريبية)');
    
    // مسح الحقول
    document.getElementById('passwordForm').reset();
}

// التركيز على أول حقل
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('full_name').focus();
});
</script>
{% endblock %}
