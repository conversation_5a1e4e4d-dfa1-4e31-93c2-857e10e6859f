<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}نظام الشؤون القانونية{% endblock %}</title>

    <!-- Bootstrap CSS (RTL) -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- Print Styles -->
    <link
      href="{{ url_for('static', filename='css/print.css') }}"
      rel="stylesheet"
      media="print"
    />
    <!-- Google Fonts - Arabic -->
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap"
      rel="stylesheet"
    />

    <style>
      :root {
        /* Primary and secondary colors with fallback */
        --bs-primary: {% if get_system_settings %}{{ get_system_settings().primary_color or '#007bff' }}{% else %}#007bff{% endif %};
        --bs-secondary: {% if get_system_settings %}{{ get_system_settings().secondary_color or '#6c757d' }}{% else %}#6c757d{% endif %};
        --primary-color: {% if get_system_settings %}{{ get_system_settings().primary_color or '#007bff' }}{% else %}#007bff{% endif %};
        --secondary-color: {% if get_system_settings %}{{ get_system_settings().secondary_color or '#6c757d' }}{% else %}#6c757d{% endif %};

        /* Standard color palette */
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
        --light-color: #f8f9fa;
        --dark-color: #343a40;

        /* متغيرات إضافية للتصميم */
        --border-radius: 8px;
        --border-radius-lg: 15px;
        --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        --box-shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
        --transition: all 0.3s ease;
        --font-family: 'Cairo', sans-serif;
      }

      /* تنسيق التنبيهات */
      .notification-dropdown {
        border: none;
        box-shadow: var(--box-shadow-lg);
        border-radius: var(--border-radius);
      }

      .notification-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        transition: var(--transition);
      }

      .notification-item:hover {
        background-color: #f8f9fa;
      }

      .notification-item.unread {
        background-color: #e3f2fd;
        border-left: 4px solid var(--primary-color);
      }

      .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 12px;
        flex-shrink: 0;
      }

      .notification-content {
        flex: 1;
      }

      .notification-title {
        font-weight: 600;
        font-size: 14px;
        color: var(--dark-color);
        margin-bottom: 4px;
      }

      .notification-text {
        font-size: 13px;
        color: #6c757d;
        margin-bottom: 4px;
        line-height: 1.4;
      }

      .notification-time {
        font-size: 12px;
        color: #9ca3af;
      }

      #notificationCount {
        font-size: 10px;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .notification-bell {
        position: relative;
        animation: bellRing 2s infinite;
      }

      @keyframes bellRing {
        0%, 50%, 100% { transform: rotate(0deg); }
        10%, 30% { transform: rotate(10deg); }
        20% { transform: rotate(-10deg); }
      }

      body {
        font-family: var(--font-family);
        background-color: var(--light-color);
        color: var(--dark-color);
        transition: var(--transition);
      }

      .navbar-brand {
        font-weight: 700;
        font-size: 1.5rem;
      }

      .navbar-brand img {
        background: white;
        border-radius: 50%;
        padding: 5px;
        box-shadow: var(--box-shadow);
        border: 2px solid var(--light-color);
        transition: var(--transition);
      }

      .navbar-brand img:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(var(--bs-primary), 0.3);
      }

      .navbar-brand-logo {
        background: white;
        border-radius: 50%;
        padding: 5px;
        box-shadow: var(--box-shadow);
        border: 2px solid var(--light-color);
        transition: var(--transition);
        max-height: 50px;
        width: auto;
      }

      .navbar-brand-logo:hover {
        transform: scale(1.05);
        box-shadow: var(--box-shadow-lg);
      }

      /* خلفية دائرية للشعارات في جميع أنحاء النظام */
      .logo-circular {
        background: white;
        border-radius: 50%;
        padding: 5px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border: 2px solid #f8f9fa;
        transition: all 0.3s ease;
      }

      .logo-circular:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
      }

      /* خلفية دائرية للشعارات الصغيرة */
      .logo-circular-sm {
        background: white;
        border-radius: 50%;
        padding: 3px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
      }

      /* خلفية دائرية للشعارات الكبيرة */
      .logo-circular-lg {
        background: white;
        border-radius: 50%;
        padding: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        border: 3px solid #f8f9fa;
        transition: all 0.3s ease;
      }

      .logo-circular-lg:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
      }

      /* إصلاح مشاكل التصميم */
      .navbar-brand img {
        max-height: 50px;
        width: auto;
      }

      .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.9);
        padding: 12px 20px;
        margin: 2px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .sidebar .nav-link:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transform: translateX(5px);
      }

      .sidebar .nav-link.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
      }

      /* تحسين الكروت */
      .card {
        border: none;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--box-shadow);
        transition: var(--transition);
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: var(--box-shadow-lg);
      }

      .sidebar {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        box-shadow: var(--box-shadow);
      }

      .sidebar .nav-link {
        color: rgba(255, 255, 255, 0.8);
        padding: 12px 20px;
        margin: 2px 0;
        border-radius: 8px;
        transition: all 0.3s ease;
      }

      .sidebar .nav-link:hover,
      .sidebar .nav-link.active {
        color: white;
        background-color: rgba(165, 163, 163, 0.1);
        transform: translateX(-5px);
      }

      .sidebar .nav-link i {
        margin-left: 10px;
        width: 20px;
        text-align: center;
      }

      .main-content {
        padding: 20px;
      }

      .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
      }

      .card-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
        font-weight: 600;
      }

      .btn-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border: none;
        border-radius: var(--border-radius);
        padding: 10px 20px;
        font-weight: 600;
        transition: var(--transition);
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .table {
        border-radius: 10px;
        overflow: hidden;
      }

      .table thead th {
        background-color: #f8f9fa;
        border: none;
        font-weight: 600;
      }

      .alert {
        border: none;
        border-radius: 10px;
      }

      .stats-card {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        border-radius: var(--border-radius-lg);
        padding: 20px;
        margin-bottom: 20px;
        transition: var(--transition);
      }

      .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--box-shadow-lg);
      }

      .stats-card .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
      }

      .stats-card .stats-label {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .form-control,
      .form-select {
        border-radius: var(--border-radius);
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: var(--transition);
      }

      .form-control:focus,
      .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-color), 0.25);
      }

      .badge {
        font-size: 0.8rem;
        padding: 6px 12px;
        border-radius: 20px;
      }

      .status-pending {
        background-color: #ffc107;
      }
      .status-active {
        background-color: #17a2b8;
      }
      .status-completed {
        background-color: #28a745;
      }
      .status-cancelled {
        background-color: #dc3545;
      }

      @media (max-width: 768px) {
        .sidebar {
          position: fixed;
          top: 0;
          right: -250px;
          width: 250px;
          height: 100vh;
          z-index: 1000;
          transition: right 0.3s ease;
        }

        .sidebar.show {
          right: 0;
        }

        .main-content {
          margin-right: 0;
        }
      }
    </style>

    {% block extra_css %}{% endblock %}
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container-fluid">
        <button
          class="navbar-toggler d-lg-none"
          type="button"
          onclick="toggleSidebar()"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <a
          class="navbar-brand d-flex align-items-center"
          href="{{ {% url 'dashboard' %} }}"
        >
          {% if get_logo_settings %} {% set logo_settings = get_logo_settings()
          %} {% set current_logo = get_current_logo() %} {% else %} {% set
          logo_settings = {'height': 40, 'padding': 5, 'border_width': 2,
          'shadow': 8, 'circular': true} %} {% set current_logo =
          '/static/images/default-logo.svg' %} {% endif %}
          <img
            src="{{ current_logo or '/static/images/default-logo.svg' }}"
            alt="الشعار"
            height="{{ logo_settings.height or 40 }}px"
            class="me-2 navbar-brand-logo"
          />
          <span class="d-none d-md-inline">
            {% if get_system_settings %} {% set system_settings =
            get_system_settings() %} {{ system_settings.system_name or 'نظام
            الشؤون القانونية' }} {% else %} نظام الشؤون القانونية {% endif %}
          </span>
        </a>

        <div class="navbar-nav ms-auto">
          {% if current_user.is_authenticated %}
          <!-- زر التنبيهات -->
          <div class="nav-item dropdown me-3">
            <a
              class="nav-link position-relative"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
              id="notificationsDropdown"
              title="التنبيهات"
            >
              <i class="fas fa-bell fs-5"></i>
              <span
                class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                id="notificationCount"
                style="display: none"
              >
                0
              </span>
            </a>
            <ul
              class="dropdown-menu dropdown-menu-end notification-dropdown"
              style="width: 350px; max-height: 400px; overflow-y: auto"
            >
              <li
                class="dropdown-header d-flex justify-content-between align-items-center"
              >
                <span><i class="fas fa-bell me-2"></i>التنبيهات</span>
                <button
                  class="btn btn-sm btn-outline-primary"
                  onclick="markAllAsRead()"
                >
                  <i class="fas fa-check-double me-1"></i>قراءة الكل
                </button>
              </li>
              <li><hr class="dropdown-divider" /></li>

              <!-- التنبيهات -->
              <li>
                <div class="dropdown-item-text text-center py-4">
                  <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                  <div class="text-muted">لا توجد تنبيهات جديدة</div>
                  <small class="text-muted"
                    >ستظهر التنبيهات هنا عند وجودها</small
                  >
                </div>
              </li>

              <li><hr class="dropdown-divider" /></li>
              <li>
                <a class="dropdown-item text-center" href="/notifications">
                  <i class="fas fa-eye me-1"></i>عرض جميع التنبيهات
                </a>
              </li>
            </ul>
          </div>

          <div class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-user me-1"></i>
              {{ current_user.full_name }}
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <a class="dropdown-item" href="/admin/profile"
                  ><i class="fas fa-user me-2"></i>الملف الشخصي</a
                >
              </li>
              {% if current_user.role == 'admin' %}
              <li>
                <a class="dropdown-item" href="/admin/settings"
                  ><i class="fas fa-cog me-2"></i>إعدادات النظام</a
                >
              </li>
              {% endif %}
              <li><hr class="dropdown-divider" /></li>
              <li>
                <a class="dropdown-item" href="{{ {% url 'logout' %} }}"
                  ><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a
                >
              </li>
            </ul>
          </div>
          {% endif %}
        </div>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row">
        {% if current_user.is_authenticated %}
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar" id="sidebar">
          <div class="position-sticky pt-3">
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link" href="{{ {% url 'dashboard' %} }}">
                  <i class="fas fa-tachometer-alt"></i>
                  لوحة التحكم
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/cases/">
                  <i class="fas fa-folder-open"></i>
                  القضايا
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/contracts/">
                  <i class="fas fa-file-contract"></i>
                  العقود
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/clients/">
                  <i class="fas fa-users"></i>
                  العملاء
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/employees/">
                  <i class="fas fa-id-badge"></i>
                  الموظفين
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ {% url 'attendance_index' %} }}">
                  <i class="fas fa-clock"></i>
                  الحضور والغياب
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ {% url 'leaves_index' %} }}">
                  <i class="fas fa-calendar-alt"></i>
                  الإجازات والاستئذان
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ {% url 'warnings_index' %} }}">
                  <i class="fas fa-exclamation-triangle"></i>
                  الإنذارات
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ {% url 'penalties_index' %} }}">
                  <i class="fas fa-gavel"></i>
                  الجزاءات
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/lawyers/">
                  <i class="fas fa-user-tie"></i>
                  المحامون
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/appointments/">
                  <i class="fas fa-calendar-alt"></i>
                  المواعيد
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/documents/">
                  <i class="fas fa-file-alt"></i>
                  المستندات
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/invoices/">
                  <i class="fas fa-file-invoice-dollar"></i>
                  الفواتير
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/reports/">
                  <i class="fas fa-chart-bar"></i>
                  التقارير
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ {% url 'settings_index' %} }}">
                  <i class="fas fa-sliders-h"></i>
                  إعدادات النظام
                </a>
              </li>

              {% if current_user.role == 'admin' %}
              <li class="nav-item dropdown">
                <a
                  class="nav-link dropdown-toggle"
                  href="#"
                  id="adminDropdown"
                  role="button"
                  data-bs-toggle="dropdown"
                >
                  <i class="fas fa-cogs"></i>
                  الإدارة
                </a>
                <ul class="dropdown-menu">
                  <li>
                    <a class="dropdown-item" href="/admin/"
                      ><i class="fas fa-tachometer-alt me-2"></i>لوحة تحكم
                      المدير</a
                    >
                  </li>
                  <li>
                    <a class="dropdown-item" href="/admin/users"
                      ><i class="fas fa-users me-2"></i>إدارة المستخدمين</a
                    >
                  </li>
                  <li>
                    <a class="dropdown-item" href="/admin/permissions"
                      ><i class="fas fa-shield-alt me-2"></i>إدارة الصلاحيات</a
                    >
                  </li>
                  <li>
                    <a class="dropdown-item" href="/admin/settings"
                      ><i class="fas fa-cog me-2"></i>إعدادات النظام</a
                    >
                  </li>
                  <li>
                    <a class="dropdown-item" href="/admin/backup"
                      ><i class="fas fa-database me-2"></i>النسخ الاحتياطية</a
                    >
                  </li>
                  <li>
                    <a class="dropdown-item" href="/admin/logs"
                      ><i class="fas fa-file-alt me-2"></i>سجلات النظام</a
                    >
                  </li>
                </ul>
              </li>
              {% endif %}
            </ul>
          </div>
        </nav>
        {% endif %}

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
          <!-- Flash messages -->
          {% with messages = get_flashed_messages(with_categories=true) %} {% if
          messages %} {% for category, message in messages %}
          <div
            class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
            role="alert"
          >
            {{ message }}
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="alert"
            ></button>
          </div>
          {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock
          %}
        </main>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
      function toggleSidebar() {
        document.getElementById("sidebar").classList.toggle("show");
      }

      // Auto-hide alerts after 5 seconds
      setTimeout(function () {
        $(".alert").fadeOut("slow");
      }, 5000);

      // وظائف التنبيهات
      function markAsRead(element, notificationId) {
        // إزالة كلاس unread
        element.classList.remove("unread");

        // تحديث العداد
        updateNotificationCount();

        // إرسال طلب للخادم لتحديث حالة التنبيه
        fetch("/api/notifications/mark-read", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            notification_id: notificationId,
          }),
        }).catch((error) => {});

        // إظهار رسالة نجاح
        showAlert("success", "تم وضع علامة قراءة على التنبيه");
      }

      function markAllAsRead() {
        // إزالة كلاس unread من جميع التنبيهات
        document
          .querySelectorAll(".notification-item.unread")
          .forEach((item) => {
            item.classList.remove("unread");
          });

        // تحديث العداد
        updateNotificationCount();

        // إرسال طلب للخادم
        fetch("/api/notifications/mark-all-read", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }).catch((error) => {});

        // إظهار رسالة نجاح
        showAlert("success", "تم وضع علامة قراءة على جميع التنبيهات");
      }

      function updateNotificationCount() {
        const unreadCount = document.querySelectorAll(
          ".notification-item.unread"
        ).length;
        const countElement = document.getElementById("notificationCount");

        if (unreadCount > 0) {
          countElement.textContent = unreadCount;
          countElement.style.display = "flex";
        } else {
          countElement.style.display = "none";
        }
      }

      // دالة لإضافة تنبيه جديد
      function addNotification(type, title, message, time = "الآن") {
        const notificationsList = document.querySelector(
          ".notification-dropdown"
        );
        const newNotification = document.createElement("li");

        const iconClass =
          type === =  "appointment"
            ? "calendar-alt"
            : type === =  "court"
            ? "exclamation-triangle"
            : type === =  "document"
            ? "file-alt"
            : type === =  "payment"
            ? "dollar-sign"
            : "info-circle";

        const bgClass =
          type === =  "appointment"
            ? "bg-primary"
            : type === =  "court"
            ? "bg-warning"
            : type === =  "document"
            ? "bg-success"
            : type === =  "payment"
            ? "bg-info"
            : "bg-secondary";

        newNotification.innerHTML = `
          <a class="dropdown-item notification-item unread" href="#" onclick="markAsRead(this, ${Date.now()})">
            <div class="d-flex">
              <div class="notification-icon ${bgClass}">
                <i class="fas fa-${iconClass} text-white"></i>
              </div>
              <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-text">${message}</div>
                <div class="notification-time">${time}</div>
              </div>
            </div>
          </a>
        `;

        // إدراج التنبيه في المقدمة
        const firstNotification =
          notificationsList.querySelector(".notification-item");
        if (firstNotification) {
          firstNotification.parentNode.insertBefore(
            newNotification,
            firstNotification.parentNode
          );
        }

        // تحديث العداد
        updateNotificationCount();

        // إضافة تأثير الرنين للجرس
        const bellIcon = document.querySelector("#notificationsDropdown i");
        bellIcon.classList.add("notification-bell");
        setTimeout(() => {
          bellIcon.classList.remove("notification-bell");
        }, 2000);
      }

      // دالة لإظهار التنبيهات (مشتركة)
      function showAlert(type, message) {
        const alertDiv = document.createElement("div");
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText =
          "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";

        const icon =
          type === =  "success"
            ? "check-circle"
            : type === =  "error"
            ? "exclamation-triangle"
            : type === =  "warning"
            ? "exclamation-circle"
            : "info-circle";

        alertDiv.innerHTML = `
            <i class="fas fa-${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
          if (alertDiv.parentNode) {
            alertDiv.remove();
          }
        }, 5000);
      }

      // تحديث العداد عند تحميل الصفحة
      document.addEventListener("DOMContentLoaded", function () {
        updateNotificationCount();
      });

      // يمكن إضافة تنبيهات جديدة باستخدام دالة addNotification
      // مثال: addNotification('appointment', 'موعد جديد', 'تم حجز موعد جديد');
    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
